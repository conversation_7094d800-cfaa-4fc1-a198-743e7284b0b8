#!/usr/bin/env python3
"""
Dify配置验证脚本
用于验证dify配置的保存和加载功能是否正常
"""

import json
import os
import requests
import time

def test_config_save_load():
    """测试配置保存和加载功能"""
    print("🔍 开始验证Dify配置功能...")
    
    # 检查配置文件
    config_file = "dify_config.json"
    print(f"📁 配置文件路径: {config_file}")
    
    if os.path.exists(config_file):
        print("✅ 配置文件存在")
        with open(config_file, 'r', encoding='utf-8') as f:
            current_config = json.load(f)
        print(f"📄 当前配置: {json.dumps(current_config, indent=2, ensure_ascii=False)}")
    else:
        print("❌ 配置文件不存在")
        current_config = {}
    
    return current_config

def test_api_endpoint():
    """测试API端点是否响应"""
    print("\n🌐 测试配置UI API端点...")
    
    try:
        # 测试GET请求
        response = requests.get("http://localhost:8080/dify", timeout=5)
        if response.status_code == 200:
            print("✅ GET /dify 端点正常响应")
            
            # 检查页面是否包含配置表单
            if 'dify_api_url' in response.text:
                print("✅ 配置表单正常加载")
            else:
                print("❌ 配置表单未找到")
        else:
            print(f"❌ GET /dify 响应异常: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到配置UI服务 (http://localhost:8080)")
        print("💡 请确保配置UI服务正在运行")
        return False
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False
    
    return True

def test_config_post():
    """测试配置保存功能"""
    print("\n💾 测试配置保存功能...")
    
    # 测试数据
    test_data = {
        'dify_api_url': 'https://test-verify.dify.ai/v1',
        'dify_api_key': 'verify-test-key-' + str(int(time.time())),
        'dify_app_id': 'verify-test-app-' + str(int(time.time())),
        'workflow_enabled': 'true'
    }
    
    try:
        response = requests.post(
            "http://localhost:8080/dify",
            data=test_data,
            timeout=10
        )
        
        if response.status_code == 200:
            print("✅ POST /dify 请求成功")
            
            # 检查配置文件是否更新
            time.sleep(1)  # 等待文件写入
            if os.path.exists("dify_config.json"):
                with open("dify_config.json", 'r', encoding='utf-8') as f:
                    saved_config = json.load(f)
                
                if saved_config.get('api_url') == test_data['dify_api_url']:
                    print("✅ 配置保存成功")
                    print(f"📄 保存的配置: {json.dumps(saved_config, indent=2, ensure_ascii=False)}")
                    return True
                else:
                    print("❌ 配置保存失败 - 文件内容不匹配")
            else:
                print("❌ 配置保存失败 - 文件未创建")
        else:
            print(f"❌ POST /dify 请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 保存测试失败: {e}")
    
    return False

def main():
    """主函数"""
    print("=" * 50)
    print("🤖 Dify配置功能验证工具")
    print("=" * 50)
    
    # 1. 检查当前配置
    current_config = test_config_save_load()
    
    # 2. 测试API端点
    if not test_api_endpoint():
        print("\n❌ 验证失败：配置UI服务未运行")
        print("💡 请先运行: python config_ui.py 或 ./start-config-ui.sh")
        return
    
    # 3. 测试配置保存
    if test_config_post():
        print("\n✅ 所有测试通过！Dify配置功能正常工作")
    else:
        print("\n❌ 配置保存功能存在问题")
    
    print("\n" + "=" * 50)
    print("验证完成")

if __name__ == "__main__":
    main()
