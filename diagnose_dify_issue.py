#!/usr/bin/env python3
"""
Dify配置问题诊断脚本
专门用于诊断用户反馈的"Dify配置不完整"和"配置信息丢失"问题
"""

import json
import os
import requests
import time
import subprocess
import sys

def check_file_permissions():
    """检查文件权限"""
    print("🔐 检查文件权限...")
    
    config_file = "dify_config.json"
    
    if os.path.exists(config_file):
        # 检查读权限
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                content = f.read()
            print("✅ 配置文件可读")
        except Exception as e:
            print(f"❌ 配置文件读取失败: {e}")
            return False
        
        # 检查写权限
        try:
            # 备份原内容
            with open(config_file, 'r', encoding='utf-8') as f:
                original_content = f.read()
            
            # 尝试写入
            with open(config_file, 'w', encoding='utf-8') as f:
                f.write(original_content)
            print("✅ 配置文件可写")
            return True
        except Exception as e:
            print(f"❌ 配置文件写入失败: {e}")
            return False
    else:
        # 尝试创建文件
        try:
            test_config = {"test": "permission_check"}
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(test_config, f)
            os.remove(config_file)
            print("✅ 可以创建配置文件")
            return True
        except Exception as e:
            print(f"❌ 无法创建配置文件: {e}")
            return False

def check_running_processes():
    """检查运行中的进程"""
    print("\n🔍 检查运行中的Python进程...")
    
    try:
        result = subprocess.run(['ps', 'aux'], capture_output=True, text=True)
        python_processes = []
        
        for line in result.stdout.split('\n'):
            if 'python' in line.lower() and ('config_ui' in line or 'simple_config_ui' in line):
                python_processes.append(line.strip())
        
        if python_processes:
            print(f"✅ 找到 {len(python_processes)} 个配置UI进程:")
            for i, process in enumerate(python_processes, 1):
                print(f"   {i}. {process}")
        else:
            print("❌ 未找到配置UI进程")
            return False
        
        return True
    except Exception as e:
        print(f"❌ 检查进程失败: {e}")
        return False

def check_working_directory():
    """检查工作目录"""
    print("\n📁 检查工作目录...")
    
    current_dir = os.getcwd()
    print(f"当前工作目录: {current_dir}")
    
    # 检查关键文件是否存在
    key_files = ['config_ui.py', 'simple_config_ui.py', 'templates/dify.html']
    missing_files = []
    
    for file in key_files:
        if os.path.exists(file):
            print(f"✅ {file} 存在")
        else:
            print(f"❌ {file} 不存在")
            missing_files.append(file)
    
    if missing_files:
        print(f"⚠️  缺少关键文件，可能不在正确的工作目录中")
        return False
    
    return True

def test_browser_cache():
    """测试浏览器缓存问题"""
    print("\n🌐 测试浏览器缓存问题...")
    
    try:
        # 添加随机参数避免缓存
        timestamp = str(int(time.time()))
        response = requests.get(f"http://localhost:8080/dify?_t={timestamp}", timeout=5)
        
        if response.status_code == 200:
            print("✅ 配置页面可访问")
            
            # 检查是否包含配置数据
            if 'dify_api_url' in response.text:
                print("✅ 配置表单正常加载")
                
                # 检查是否有配置值
                if 'value=""' in response.text:
                    print("⚠️  发现空的配置值，可能是配置未正确加载")
                    return False
                else:
                    print("✅ 配置值正常显示")
                    return True
            else:
                print("❌ 配置表单未找到")
                return False
        else:
            print(f"❌ 配置页面访问失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 浏览器缓存测试失败: {e}")
        return False

def check_config_ui_version():
    """检查配置UI版本"""
    print("\n🔧 检查配置UI版本...")
    
    config_ui_files = ['config_ui.py', 'simple_config_ui.py']
    
    for file in config_ui_files:
        if os.path.exists(file):
            print(f"✅ {file} 存在")
            
            # 检查文件修改时间
            mtime = os.path.getmtime(file)
            mtime_str = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(mtime))
            print(f"   最后修改时间: {mtime_str}")
        else:
            print(f"❌ {file} 不存在")

def provide_solutions():
    """提供解决方案"""
    print("\n" + "=" * 50)
    print("🛠️  问题解决方案")
    print("=" * 50)
    
    print("\n1. 🔄 重启配置UI服务")
    print("   - 停止所有Python进程: pkill -f python")
    print("   - 重新启动: python config_ui.py")
    
    print("\n2. 🧹 清除浏览器缓存")
    print("   - Chrome/Edge: Ctrl+Shift+R (Windows) 或 Cmd+Shift+R (Mac)")
    print("   - Firefox: Ctrl+F5 (Windows) 或 Cmd+Shift+R (Mac)")
    print("   - 或使用无痕/隐私模式")
    
    print("\n3. 📁 检查工作目录")
    print("   - 确保在项目根目录运行配置UI")
    print("   - 检查 dify_config.json 文件是否在正确位置")
    
    print("\n4. 🔐 检查文件权限")
    print("   - 确保对 dify_config.json 有读写权限")
    print("   - 如果需要: chmod 644 dify_config.json")
    
    print("\n5. 🔍 使用正确的启动方式")
    print("   - 推荐: ./start-config-ui.sh")
    print("   - 或者: python config_ui.py")
    print("   - 避免同时运行多个配置UI实例")

def main():
    """主函数"""
    print("=" * 60)
    print("🔍 Dify配置问题诊断工具")
    print("=" * 60)
    print("专门诊断'Dify配置不完整'和'配置信息丢失'问题")
    
    issues_found = []
    
    # 1. 检查工作目录
    if not check_working_directory():
        issues_found.append("工作目录问题")
    
    # 2. 检查文件权限
    if not check_file_permissions():
        issues_found.append("文件权限问题")
    
    # 3. 检查运行进程
    if not check_running_processes():
        issues_found.append("配置UI服务未运行")
    
    # 4. 检查浏览器缓存
    if not test_browser_cache():
        issues_found.append("浏览器缓存或配置加载问题")
    
    # 5. 检查配置UI版本
    check_config_ui_version()
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 诊断结果")
    print("=" * 60)
    
    if issues_found:
        print(f"❌ 发现 {len(issues_found)} 个问题:")
        for i, issue in enumerate(issues_found, 1):
            print(f"   {i}. {issue}")
        
        provide_solutions()
    else:
        print("✅ 未发现明显问题，Dify配置功能应该正常工作")
        print("\n💡 如果仍然遇到问题，请尝试:")
        print("   1. 完全重启浏览器")
        print("   2. 使用不同的浏览器测试")
        print("   3. 检查是否有防火墙或安全软件干扰")
    
    print("\n" + "=" * 60)
    print("诊断完成")

if __name__ == "__main__":
    main()
